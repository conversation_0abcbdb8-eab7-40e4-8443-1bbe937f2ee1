import { AppEventType } from '../../../app/types/AppEventType.js'
import { BgChannelChangedEvent } from '../../bgChannels/types/classes/BgChannelChangedEvent.js'
import { ObjectChangedEvent } from '../../models/types/classes/ObjectChangedEvent.js'

type PubSubPublishArgsByKey = {
  [key: string]: [] | [any] | [number | string, any];
};

export interface SubscriptionsType extends PubSubPublishArgsByKey {
  [AppEventType.objectChanged]: [ObjectChangedEvent];
  [AppEventType.channelChanged]: [BgChannelChangedEvent];
}
