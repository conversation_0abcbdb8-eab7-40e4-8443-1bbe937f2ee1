import { expect } from 'chai'
import { PubAck } from '@nats-io/jetstream'
import * as jetstream from '@nats-io/jetstream'

import { buildEntityName } from '../../serviceHelpers/buildEntityName.js'
import { createStream } from '../../operations/createStream.js'
import { deleteStream } from '../../operations/deleteStream.js'
import { NatsClient } from '../../serviceHelpers/NatsClient.js'
import { NatsServiceConfig } from '../../types/NatsServiceConfig.js'
import { publish } from '../../operations/publish.js'
import { consumeStream } from '../../operations/consumeStream.js'
import { ModelType } from '../../../../app/types/ModelType.js'
import { ServiceRequestSource } from '../../../../app/types/ServiceRequestSource.js'
import * as Models from '../../../models/types/index.js'
import logger from '../../../logger/index.js'
import serviceData from '../../serviceHelpers/serviceData.js'
import serviceHelpers from '../../serviceHelpers/index.js'

const systemServiceRequest = new Models.ServiceRequest({ source: ServiceRequestSource.system })

describe('services.nats.operations.comprehensive', () => {
  let natsClient: NatsClient
  let testStreamName: string
  let testSubject: string
  let publishedMessages: any[] = []
  let receivedMessages: any[] = []

  before(async () => {
    // Initialize NATS client with test configuration
    const serviceConfig = serviceHelpers.getConfig() as NatsServiceConfig

    if (!serviceConfig || !serviceConfig.natsOptions.servers || serviceConfig.natsOptions.servers.length === 0) {
      throw new Error('NATS configuration not found or invalid')
    }

    logger.info('comprehensive.spec: Initializing NATS client', { serviceConfig })

    // Create and connect NATS client
    natsClient = new NatsClient(serviceConfig.natsOptions)
    await natsClient.connect()

    if (!natsClient.connected) {
      throw new Error('Failed to connect to NATS server')
    }

    // Set the client in service data so other operations can use it
    serviceData.setNatsClient(natsClient)

    logger.info('comprehensive.spec: NATS client connected successfully')
  })

  after(async () => {
    // Clean up: delete test stream and disconnect
    if (testStreamName) {
      try {
        await deleteStream(testStreamName as any)
        logger.info('comprehensive.spec: Test stream deleted', { testStreamName })
      } catch (error) {
        logger.warn('comprehensive.spec: Failed to delete test stream', { testStreamName, error })
      }
    }

    if (natsClient) {
      await natsClient.close()
      logger.info('comprehensive.spec: NATS client disconnected')
    }
  })

  it('should initialize and connect with NATS server', () => {
    expect(natsClient).to.exist
    expect(natsClient.connected).to.be.true
    logger.info('comprehensive.spec: ✓ NATS client connection verified')
  })

  it('should create a stream', async () => {
    testStreamName = 'test-comprehensive-stream'
    testSubject = 'test.comprehensive.*'

    const streamConfig: jetstream.StreamConfig = {
      name: testStreamName,
      subjects: [testSubject],
      retention: jetstream.RetentionPolicy.Workqueue,
      storage: jetstream.StorageType.Memory,
      max_msgs: 1000,
      max_age: 60 * 60 * 1000 * 1000 * 1000, // 1 hour in nanoseconds
    }

    const streamInfo = await createStream(streamConfig)

    expect(streamInfo).to.exist
    expect(streamInfo.config.name).to.equal(buildEntityName(testStreamName, true))
    expect(streamInfo.config.subjects).to.include(buildEntityName(testSubject, false))

    logger.info('comprehensive.spec: ✓ Stream created successfully', {
      streamName: streamInfo.config.name,
      subjects: streamInfo.config.subjects
    })
  })

  it('should subscribe to the stream and consume messages', async function() {
    this.timeout(10000) // Increase timeout for async operations

    const consumerName = 'test-comprehensive-consumer'
    const maxMessages = 5

    // Start consuming messages in the background
    const consumePromise = (async () => {
      const messages = await consumeStream(
        testStreamName as any,
        undefined, // No specific subject filter
        consumerName,
        {
          maxMessages,
          expires: 8000, // 8 seconds - increased timeout
          idleHeartbeat: 1000, // 1 second heartbeat - reduced from default
        }
      )

      for await (const message of messages) {
        try {
          const dataString = new TextDecoder().decode(message.data)
          const payload = JSON.parse(dataString)
          receivedMessages.push({
            subject: message.subject,
            payload,
            seq: message.seq,
            timestamp: Date.now()
          })

          logger.info('comprehensive.spec: Message received', {
            subject: message.subject,
            payload,
            seq: message.seq
          })

          // Acknowledge the message
          message.ack()

          // Stop after receiving expected number of messages
          if (receivedMessages.length >= maxMessages) {
            break
          }
        } catch (error) {
          logger.error('comprehensive.spec: Error processing message', { error })
          message.nak()
        }
      }
    })()

    // Give the consumer a moment to set up
    await new Promise(resolve => setTimeout(resolve, 500))

    logger.info('comprehensive.spec: ✓ Stream subscription established')

    // Return the promise so the test can wait for it if needed
    return consumePromise
  })

  it('should publish messages to the stream', async function() {
    this.timeout(10000)

    const testMessages = [
      {
        subject: 'test.comprehensive.user.created',
        payload: {
          objectId: 'user-001',
          modelType: ModelType.User,
          changeType: 'created',
          serviceRequest: systemServiceRequest,
          timestamp: Date.now()
        }
      },
      {
        subject: 'test.comprehensive.user.updated',
        payload: {
          objectId: 'user-001',
          modelType: ModelType.User,
          changeType: 'updated',
          serviceRequest: systemServiceRequest,
          timestamp: Date.now()
        }
      },
      {
        subject: 'test.comprehensive.user.deleted',
        payload: {
          objectId: 'user-001',
          modelType: ModelType.User,
          changeType: 'deleted',
          serviceRequest: systemServiceRequest,
          timestamp: Date.now()
        }
      },
      {
        subject: 'test.comprehensive.data.sync',
        payload: {
          action: 'sync',
          data: { key: 'value', number: 42, array: [1, 2, 3] },
          timestamp: Date.now()
        }
      },
      {
        subject: 'test.comprehensive.notification',
        payload: {
          type: 'info',
          message: 'Test notification message',
          userId: 'user-001',
          timestamp: Date.now()
        }
      }
    ]

    // Publish all messages
    for (const testMessage of testMessages) {
      await new Promise<void>((resolve, reject) => {
        publish(
          testMessage.subject,
          testMessage.payload,
          undefined,
          (error?: Error | null, ack?: PubAck) => {
            if (error) {
              logger.error('comprehensive.spec: Error publishing message', {
                subject: testMessage.subject,
                error
              })
              reject(error)
            } else {
              publishedMessages.push({
                subject: testMessage.subject,
                payload: testMessage.payload,
                ack,
                timestamp: Date.now()
              })

              logger.info('comprehensive.spec: Message published', {
                subject: testMessage.subject,
                seq: ack?.seq,
                stream: ack?.stream
              })

              expect(ack?.seq).to.be.a('number')
              expect(ack?.stream).to.equal(buildEntityName(testStreamName, true))
              expect(ack?.duplicate).to.be.false

              resolve()
            }
          }
        )
      })

      // Small delay between messages to ensure ordering
      await new Promise(resolve => setTimeout(resolve, 50))
    }

    expect(publishedMessages).to.have.length(testMessages.length)
    logger.info('comprehensive.spec: ✓ All messages published successfully', {
      count: publishedMessages.length
    })
  })

  it('should compare published messages with subscribed stream messages', async function() {
    this.timeout(15000)

    // Wait a bit more for all messages to be processed
    await new Promise(resolve => setTimeout(resolve, 2000))

    logger.info('comprehensive.spec: Comparing messages', {
      publishedCount: publishedMessages.length,
      receivedCount: receivedMessages.length
    })

    // Verify we received the expected number of messages
    expect(receivedMessages).to.have.length(publishedMessages.length)

    // Sort both arrays by sequence number for comparison
    const sortedPublished = publishedMessages.sort((a, b) => (a.ack?.seq || 0) - (b.ack?.seq || 0))
    const sortedReceived = receivedMessages.sort((a, b) => a.seq - b.seq)

    // Compare each message
    for (let i = 0; i < sortedPublished.length; i++) {
      const published = sortedPublished[i]
      const received = sortedReceived[i]

      // Verify subject matches (accounting for entity name prefixing)
      expect(received.subject).to.equal(buildEntityName(published.subject, false))

      // Verify payload content matches
      expect(received.payload.objectId).to.equal(published.payload.objectId)

      if (published.payload.modelType) {
        expect(received.payload.modelType).to.equal(published.payload.modelType)
      }

      if (published.payload.changeType) {
        expect(received.payload.changeType).to.equal(published.payload.changeType)
      }

      logger.info('comprehensive.spec: Message comparison successful', {
        index: i,
        publishedSubject: published.subject,
        receivedSubject: received.subject,
        publishedSeq: published.ack?.seq,
        receivedSeq: received.seq
      })
    }

    logger.info('comprehensive.spec: ✓ All published messages match received messages')
  })

  it('should verify stream statistics', async () => {
    const jsm = await natsClient.getJetStreamManager()
    const streamInfo = await jsm.streams.info(buildEntityName(testStreamName, true))

    expect(streamInfo.state.messages).to.equal(publishedMessages.length)
    expect(streamInfo.state.consumer_count).to.be.at.least(1)

    logger.info('comprehensive.spec: ✓ Stream statistics verified', {
      messages: streamInfo.state.messages,
      consumer_count: streamInfo.state.consumer_count,
      bytes: streamInfo.state.bytes
    })
  })
})
